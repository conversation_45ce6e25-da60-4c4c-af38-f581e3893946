<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # SECURITY: Block access to sensitive directories and files
    RewriteRule ^(scripts|docs|storage|vendor|tests|database|config|bootstrap|app|resources|routes)(/.*)?$ - [F,L]
    RewriteRule ^(composer\.(json|lock)|package\.(json|lock)|\.env.*|artisan|phpunit\.xml.*|webpack\.mix\.js|vite\.config\.js)$ - [F,L]
    RewriteRule ^\. - [F,L]

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Redirect naked domain to www
    RewriteCond %{HTTP_HOST} ^rentcars-crete.com$
    RewriteRule (.*) https://www.rentcars-crete.com/$1 [R=301,L]
    # Redirect http to https
    RewriteCond %{HTTPS} off
    RewriteCond %{HTTP_HOST} ^www.rentcars-crete.com$
    RewriteRule (.*) https://www.rentcars-crete.com/$1 [R=301,L]
    # Redirect naked domain to www
    RewriteCond %{HTTP_HOST} ^cretanrentals.com$
    RewriteRule (.*) https://www.cretanrentals.com/$1 [R=301,L]
    # Redirect http to https
    RewriteCond %{HTTPS} off
    RewriteCond %{HTTP_HOST} ^www.cretanrentals.com$
    RewriteRule (.*) https://www.cretanrentals.com/$1 [R=301,L]
    # Redirect eurodollar to rentcars-crete
    RewriteCond %{HTTP_HOST} ^eurodollar.gr$
    RewriteRule (.*) https://www.rentcars-crete.com/$1 [R=301,L]
    RewriteCond %{HTTP_HOST} ^www.eurodollar.gr$
    RewriteRule (.*) https://www.rentcars-crete.com/$1 [R=301,L]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
